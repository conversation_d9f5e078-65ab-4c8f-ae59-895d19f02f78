@use "./app/styles/core";
@use "./assets/styles/toaster.scss";

@tailwind base;
@tailwind components;
@tailwind utilities;
// @import '../node_modules/@ng-select/ng-select/themes/default.theme.css';

@font-face {
    font-family: 'Antikvarika';
    src: url('/assets/fonts/antikvarika/antikvarika.eot');
    /* IE9 compatibility */
    src: url('/assets/fonts/antikvarika/antikvarika.eot?#iefix') format('embedded-opentype'),
        url('/assets/fonts/antikvarika/antikvarika.woff2') format('woff2'),
        url('/assets/fonts/antikvarika/antikvarika.woff') format('woff'),
        url('/assets/fonts/antikvarika/antikvarika.ttf') format('truetype'),
        url('/assets/fonts/antikvarika/antikvarika.svg#Antikvarika') format('svg');
    font-weight: normal;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'Montserrat';
    src: url('assets/fonts/Montserrat/Montserrat-Italic-VariableFont_wght.ttf') format('ttf'),
        url('assets/fonts/Montserrat/Montserrat-VariableFont_wght.ttf') format('ttf'),
        url('assets/fonts/Montserrat/Montserrat-VariableFont_wght.ttf') format('truetype');
    font-weight: normal;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'Montserrat';
    src: url('assets/fonts/Open_Sans/OpenSans-Italic-VariableFont_wdth\,wght.ttf') format('ttf'),
        url('assets/fonts/Open_Sans/OpenSans-VariableFont_wdth\,wght.ttf') format('ttf'),
        url('assets/fonts/Open_Sans/OpenSans-VariableFont_wdth\,wght.ttf') format('truetype');
    font-weight: normal;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'IBM_Plex_Sans';
    src: url('/assets/fonts/IBM_Plex_Sans/antikvarika.eot');
    src: url('/assets/fonts/IBM_Plex_Sans/IBMPlexSans-Regular.ttf') format('truetype');
    font-weight: normal;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: BeaumarchaisC;
    src: url('/assets/fonts/BeaumarchaisC/BEAUMARCHAISC.TTF');
    src: url('/assets/fonts/BeaumarchaisC/BEAUMARCHAISC.TTF') format('truetype');
    font-weight: normal;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: Prata;
    src: url('/assets/fonts/Prata/ofont.ru_Prata.ttf');
    src: url('/assets/fonts/Prata/ofont.ru_Prata.ttf') format('truetype');
    font-weight: normal;
    font-style: normal;
    font-display: swap;
}

.bgd {
    background-color: var(--grey-back);
    color: var(--main-text);
}

article,
aside,
details,
figcaption,
figure,
footer,
header,
hgroup,
nav,
section,
summary {
    display: block;
}

.comment-form .editor-toolbar button {
    font-family: Montserrat;
    font-weight: 600;
    font-style: SemiBold;
    font-size: 20px;
    line-height: 20px;
    color: var(--font-color1);

    &.itc {
        font-style: italic;
    }

    &.und {
        text-decoration: underline;
    }
}

.playControls {
    position: fixed;
    bottom: 0;
    visibility: hidden;
    width: 100%;
    perspective: 900;
    perspective-origin: 80% 100%;
}

select {
    color: black;
    border-radius: 5px;
}

breadcrumb {
    position: relative;
    display: block;
    width: fit-content;
    margin: 60px 0 30px 12%;
    z-index: 1001;
}

.header {
    width: 100%;
}

.center_logo {
    display: none;
    background: var(--om_main);
    background-repeat: no-repeat;
    background-position: center;
    background-size: contain;
    width: 94px;
    height: 84px;
    position: absolute;
    left: 50%;
    right: 50%;
    transform: translate(-50%, 0);
    top: -3px;
}

// .head_wrap {
//     position: static;
//     top: 0;
//     width: 100%;
//     perspective: 900;
//     perspective-origin: 80% 100%;
//     background-color: #f2f2f2;
//     border-bottom: 1px solid gray;
// }

.g-z-index-control-bar {
    z-index: 1001;
}

.playControls__wrapper {
    padding-bottom: 10px;
    visibility: visible;
    position: relative;
    height: 100%;
}

.playControls.m-visible .playControls__inner {
    transform: translateY(0);
}

.nav_playr.mobile_v {
    display: none;
}

.playControls__elements {
    display: flex;
    position: relative;
    height: 100%;
    width: 100%;
    justify-content: space-between;
    align-items: center;
    padding: 17px 0 19px 0;
}

.playControls__bg {
    position: absolute;
    left: 0;
    bottom: 0;
    right: 0;
    top: -1px;
}

.no_wrap {
    white-space: nowrap;
}

.pl_center_wrap {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 59.67%;
}

.playControls__inner {
    background: var(--pl-back-gradient);
    height: 117px;
    visibility: visible;
    transition: transform .2s ease-out;
    transform: translateY(100%);
}

.l-container {
    width: 1240px;
    margin: 0 auto;
}

.l-container.l-fullwidth {
    width: 1240px;
    padding: 0;
}

.volume,
.volume__iconWrapper {
    position: relative;
}

.volume__iconWrapper {
    display: flex;

    button {
        margin-right: 20px;
    }
}

.range_i {
    position: relative;
    transform: rotate(-90deg);
    left: -50px;
    top: 65px;
    opacity: 0;
    z-index: 4;
    cursor: pointer;
}

li.spd {
    font-family: IBM Plex Sans Hebrew;
    font-weight: 500;
    font-size: 24px;
    line-height: 24px;
    letter-spacing: 0;
    color: var(--text-color);
}

li.spd:hover {
    color: var(--text-color23);
}

li.spd.selected {
    color: var(--text-color23);
}

.speed-options {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 2px;
    justify-content: space-evenly;
    list-style: none;
    height: 100%;
}

.volume__button {
    display: block;
    border: 0;
    background: url(assets/svg_icons/vol.svg)no-repeat 0;
    outline: 0;

    &.muted {
        background: url(assets/svg_icons/novol.svg)no-repeat 0;
    }

    &.speed {
        color: #fff;
        background: none;
        font-family: IBM Plex Sans Hebrew;
        font-weight: 400;
        font-size: 24px;
        line-height: 24px;
        letter-spacing: 0;
    }
}

.style_dialog {
    position: fixed;
    top: 0;
    width: 450px;
    height: 491px;
    border-radius: 20px;
    padding: 38px 55px 10px 55px;

    .gold_ {
        font-family: Prata;
        font-weight: 400;
        font-size: 14px;
        line-height: 14px;
        color: rgb(222, 165, 61);
        margin-bottom: 6px;
    }

    input.st_inp {
        width: 344px;
        height: 50px;
        border-radius: 15px;
        border: 1px solid rgb(224, 155, 57);
        font-family: Prata;
        font-weight: 400;
        font-size: 20px;
        line-height: 24px;
        color: var(--font-color1);
        padding: 0 25px;
    }

    input.st_inp:focus-visible {
        outline: 1px solid rgb(224, 155, 57);
    }

    &.hght {
        height: 300px;
    }

    input.item_ {
        appearance: none;
        min-width: 20px;
        width: 20px;
        height: 20px;
        border: 1px solid var(--text-color);
        border-radius: 50%;
        margin-right: 22px;
        cursor: pointer;
        margin-bottom: 0;
        margin-top: 5px;

        &.mt0 {
            margin-top: 0;
        }
    }

    .button_brd {
        width: 100%;
        height: 50px;
        border-radius: 15px;
        border: 1px solid rgb(222 165 61);
        font-family: Prata;
        font-weight: 400;
        font-size: 20px;
        line-height: 20px;
        color: var(--font-color1);
    }

    .checked {
        background: var(--check);
        background-size: 20px 20px;
        background-position: center;
    }

    .stzd {
        margin-bottom: 10px;
    }
}

.h_fixed {
    height: 208px;
    overflow-x: auto;
    margin: 25px 0 20px 0;
}

.pr_20 {
    font-family: Prata;
    font-weight: 400;
    font-size: 20px;
    line-height: 24px;
    color: var(--font-color1);
}

.cl_bun {
    font-family: Prata;
    font-weight: 400;
    font-size: 20px;
    line-height: 21px;
    letter-spacing: 0;
    color: #fff;
    position: relative;
}

.cl_bun:after {
    content: "";
    position: absolute;
    top: 0;
    right: -28px;
    display: block;
    width: 20.5px;
    height: 20.5px;
    background-repeat: no-repeat;
    background-position: 50%;
    background-image: url(assets/images/icons/closee.svg);
}

.list__button {
    display: block;
    border: 0;
    background: url(assets/svg_icons/llist.svg)no-repeat 0;
    outline: 0;
    width: 24px;
    height: 46px;

    &.pressed {
        // background: url(assets/svg_icons/list.svg)no-repeat 0;
    }
}

.like__button {
    display: block;
    border: 0;
    background: url(assets/svg_icons/like.svg)no-repeat 0;
    outline: 0;
    width: 24px;
    height: 46px;
}

.volume .volume__sliderWrapper {
    height: 152px;
    overflow: visible;
    overflow: initial;
    border: none;
}

.volume .volume__sliderBackground,
.volume .volume__sliderHandle,
.volume .volume__sliderProgress {
    opacity: 1;
}

.volume__sliderWrapper:after {
    bottom: -8px;
    left: 9px;
    border-color: transparent transparent #f2f2f2 #f2f2f2;
    border-style: solid;
    border-width: 4px;
}

.volume__sliderBackground,
.volume__sliderHandle,
.volume__sliderProgress {
    opacity: 0;
    transition: opacity .1s linear;
    transition-delay: .1s;
}

.volume__sliderHandle {
    position: absolute;
    display: block;
    border-radius: 100%;
    border: 1px solid #9c9595;
    background: #f50;
    top: 0;
    z-index: 3;
    width: 8px;
    height: 8px;
    margin-left: -2.5px;
}

.volume__sliderProgress {
    bottom: 13px;
    position: absolute;
    width: 4px;
    margin-left: 18px;
    height: calc(100% - 30px);
    z-index: 2;
}

.volume__sliderProgress_bbc {
    background: #bdb6b6;
    bottom: 13px;
    position: absolute;
    width: 2px;
    margin-left: 14px;
    height: calc(100% - 30px);
    z-index: 1;
}

.volume__sliderProgress {
    background: #f50;
    width: 2px;
    margin-left: 14px;
}

.volume:hover {
    .volume__sliderWrapper:not(.list) {
        visibility: visible;
        transition: all .4s;
    }
}

.spec_onee {
    display: flex;
    height: 50px;
}

.volume__sliderWrapper {
    position: absolute;
    left: -9px;
    bottom: 50px;
    z-index: 1;
    width: 40px;
    height: 0;
    transition: height .1s;
    transform: translateZ(0);
    overflow: hidden;
    outline: 0;
    cursor: pointer;
    visibility: hidden;
    transition: all .4s;
}

.volume__sliderWrapper.list {
    display: none;
}

.volume .volume__sliderWrapper {
    height: 217px;
    width: 73px;
    border-radius: 10px;

    &.list {
        height: fit-content;
        min-height: 108px;
        overflow-y: visible;

        ul {
            overflow-y: auto;
            overflow-x: hidden;
            max-height: 477px;
        }
    }
}

.volume__sliderWrapper.list {
    position: absolute;
    left: -507px;
    bottom: 72px;
    width: 570px;
    z-index: 1;
    height: 0;
    transition: height .1s;
    transform: translateZ(0);
    overflow: visible;
    outline: 0;
    cursor: unset;
    display: block;
    visibility: hidden;
    z-index: 9999;
    border-radius: 20px;

    &.show_list {
        display: block;
        visibility: visible;
    }
}

.volume__sliderWrapper {
    left: -17px;
    width: 30px;
    bottom: 72px;
    background-color: #fff;
    box-shadow: 0 4px 25px rgba(0, 57, 100, 0.2);
}

.volume__sliderWrapper.vol_m:before {
    border-style: solid;
    border-width: 7px;
    border-left: 7px solid #000;
    border-bottom: 7px solid #000;
    border-right: 7px solid transparent;
    border-top: 7px solid transparent;
    content: "";
    position: absolute;
    width: 38px;
    height: 38px;
    box-sizing: border-box;
    transform-origin: 0 0;
    transform: rotate(-45deg);
    cursor: pointer;
    z-index: 111;
    bottom: -33px;
    right: 25px;
    border-color: transparent transparent #fff #fff;
    border-style: solid;
    border-width: 19px;
    box-shadow: -3px 3px 4px rgba(0, 0, 0, 0.1);
}

.volume__sliderWrapper:before {
    z-index: 0;
    bottom: -32px;
    right: 40px;
    border-color: #000;
    border-color: transparent transparent rgba(0, 0, 0, .15) rgba(0, 0, 0, .15);
    border-style: solid;
    border-width: 7px;
    border-left: 7px solid #000;
    border-bottom: 7px solid #000;
    border-right: 7px solid transparent;
    border-top: 7px solid transparent;
}

input:focus::placeholder {
    color: transparent !important;
}

.volume__sliderWrapper:before {
    content: "";
    position: absolute;
    width: 38px;
    height: 38px;
    box-sizing: border-box;
    transform-origin: 0 0;
    transform: rotate(-45deg);
    cursor: pointer;
    z-index: 0;
    border-color: transparent transparent #fff #fff;
    border-style: solid;
    border-width: 19px;
    box-shadow: -3px 3px 4px rgba(0, 0, 0, 0.1);
}

.skipControl__previous {
    background-image: url(assets/images/icons/ffwd.svg);

    &.skipth {
        display: none;
        background-image: var(--30sback);
        background-size: contain;
        width: 48px;
        height: 22px;
        margin: 0;
    }
}

.skipControl__next {
    background-image: url(assets/images/icons/ffwd.svg);
    transform: rotate(180deg);

    &.skipth {
        display: none;
        background-image: var(--30sforw);
        background-size: contain;
        width: 48px;
        height: 22px;
        margin: 0;
    }
}

.shuffleControl:after {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    display: block;
    width: 100%;
    height: 100%;
    background-repeat: no-repeat;
    background-position: 50%;
    transition: opacity .2s;
    background-image: url(assets/images/icons/shuffleon.svg);
    opacity: 0;
}

.repeatControl.no-repeat {
    background: url(assets/images/icons/repeat.svg);
}

.repeatControl.repeat-all {
    background-image: url(assets/svg_icons/repeatal.svg);
}

.repeatControl.repeat-one {
    background-image: url(assets/svg_icons/repeaton.svg);
}

.middle_stripe {
    max-width: 1270px;
    padding: 0 15px;
    margin: auto;
}

.carn_b_:not(.rotate) {
    margin-top: -60px;
}

.mar_md {
    margin-top: -80px;
}

.carn_b_.rotate {
    transform: rotate(180deg);
}

.ent_choose_blc {
    display: none;
}

.sel_btn {
    display: flex;
    align-items: center;
    font-family: Prata;
    font-weight: 400;
    font-size: 17px;
    line-height: 17px;
    letter-spacing: 0;
    color: var(--font-color1);
    height: 36px;
    width: 100%;
    padding-left: 34px;

    &.selected_b {
        background-color: var(--selection);
    }

    &:hover {
        background-color: var(--selection);
    }
}

.ent-mob_switch {
    display: flex;
    flex-direction: column;
    position: absolute;
    bottom: 0;
    background: rgba(254, 255, 255, 1);
    min-width: 335px;
    right: 50%;
    min-height: 143px;
    transform: translate(50%, -50%);
    border-radius: 10px;
    padding: 27px 0 17px 0;

    .x_bt {
        right: 10px;
        top: 10px;
    }
}

.thm_ .sel_btn {
    padding-left: 5px;
}

.thm_ .sel_btn.lt_set::before {
    content: '';
    background-repeat: no-repeat;
    background-position: center;
    display: flex;
    width: 24px;
    height: 24px;
    background-image: url(assets/images/icons/light_blue.svg);
    margin: 0 14px;
}

.thm_ .sel_btn.bl_set::before {
    content: '';
    background-repeat: no-repeat;
    background-position: center;
    display: flex;
    width: 24px;
    height: 24px;
    background-image: url(assets/images/icons/bw_blue.svg);
    margin: 0 14px;
}

.thm_ .sel_btn.dk_set::before {
    content: '';
    background-repeat: no-repeat;
    background-position: center;
    display: flex;
    width: 24px;
    height: 24px;
    background-image: url(assets/images/icons/dark_blue.svg);
    margin: 0 14px;
}

.carn_b.rotate {
    transform: rotate(180deg);
}

.dec_head-title_ {
    font-family: BeaumarchaisC;
    font-weight: 500 !important;
    font-size: 50px !important;
    line-height: 42px !important;
    text-align: center;
    color: var(--font-color1);
    word-break: break-word;
    padding: 14px 0 !important;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

h1.dec_head-title {
    font-family: BeaumarchaisC;
    font-weight: 500 !important;
    font-size: 55px !important;
    line-height: 36px !important;
    text-align: center;
    color: var(--font-color1);
    padding: 14px 0 !important;
}

.lt_wrap {
    max-width: 100% !important;
    overflow: hidden;
}

.dec_head {
    display: flex;
    flex-direction: column;
    align-items: center;

    &._background {
        background: url(assets/images/shine_r_.svg);
        background-position: top;
        background-repeat: no-repeat;
        height: 400px;
        display: flex;
        justify-content: center;
        margin-top: -180px;
        background-size: 702px;
    }
}

.img_description {
    font-family: IBM_Plex_Sans;
    font-size: 22px;
    font-style: italic;
    font-weight: 400;
    line-height: 28px;
    text-underline-position: from-font;
    color: #fff;
}

.quotes_ {
    position: absolute;
    top: -5px;
    left: 50%;
    right: 50%;
    transform: translate(-50%, -50%);
}

.block_quote {
    position: relative;
    width: 100%;
    border: 1px solid rgba(186, 141, 86, 1);
    padding: 50px 80px;
    margin: 50px 0;
}

.close_list {
    background: var(--x_a);
    width: 20px;
    height: 20px;
    right: 27px;
    top: 39px;
    background-size: cover;
}

.tags {
    font-family: IBM_Plex_Sans;
    font-size: 18px;
    font-weight: 400;
    line-height: 18px;
    text-decoration-style: solid;
    text-underline-position: from-font;
    color: #fff;
    margin-top: 30px;
}

.cursor-move {
    background: var(--cursor-move);
    width: 11px;
    height: 16px;
    background-size: cover;

    &.list__ {
        opacity: 0;
        margin: 0 13px 0 14px;
    }
}

.html_wrap::first-letter {
    font-family: BeaumarchaisC;
    font-weight: 400;
    font-size: 90px;
    line-height: 30px;
    text-align: justify;
    color: core.$light4;
}

.copy_ {
    background: var(--copy_);
    height: 20px;
    width: 20px;
}

.shr_ {
    background: var(--shr_);
    height: 20px;
    width: 21px;
}

.add_ {
    background: var(--add_);
    height: 20px;
    width: 17px;
}

.srch_ {
    background: var(--srch_);
    height: 20px;
    width: 21px;
}

.hor_bottom {
    border-bottom: 1px solid var(--border);

    .vert_right,
    .vert_left {
        padding-bottom: 7px;
    }
}

.hor_top {

    .vert_right,
    .vert_left {
        padding-top: 7px;
    }
}

.vert_right {
    padding-left: 6px;
    margin-right: -15px;
}

.vert_left {
    border-left: 1px solid var(--border);
    padding-left: 10px;
    margin-right: 10px;
}

.app_block {
    background-size: contain;
    background-repeat: no-repeat;
}

.arrows_cont {
    display: none;
    position: absolute;
    right: 0;
    top: 0;
}

.arr_w {
    width: 40px;
    height: 40px;
    background: url(assets/images/icons/arr_rd.svg);
    margin-left: 20px;
    cursor: pointer;

    &.trsf {
        transform: rotate(180deg);
    }
}

.wrapper_line {
    max-width: 930px;
    width: 100%;
    padding: 120px 0 165px 0;
    margin: 0 auto;
}

.repeatControl:after,
.repeatControl:before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    opacity: 0;
}

button:focus-visible {
    outline: none;
}

.repeatControl,
.repeatControl:after,
.repeatControl:before {
    background-repeat: no-repeat;
    background-position: 50%;
    cursor: pointer;
}

.repeatControl {
    margin: 0;
    padding: 0;
    position: relative;
    height: 100%;
    width: 100%;
}

.three_items {
    display: flex;
    align-items: center;
    justify-content: center;

    .frame_t {
        position: absolute;
        z-index: 11;
        width: 330px;
        height: 297px;
        background: url(assets/images/rectangle.webp);
        background-repeat: no-repeat;
        background-position: center;
        background-size: contain;
        margin: auto;
    }
}

.quick-links .link-icons.tr_ {
    display: flex;
    justify-content: space-around;
    align-items: center;
    height: 350px;
    margin-top: 108px;
}

.tr_cont {
    position: absolute;
    z-index: 12;
    width: 300px;
    margin-bottom: -24px;
}

.button_sm_wrap {
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    width: 246px;
    height: 54px;
    background: url(assets/images/button_sm.webp);
    background-repeat: no-repeat;
    background-position: center;
    background-size: contain;
    margin: auto;

    span {
        font-family: IBM_Plex_Sans;
        font-size: 17.52px;
        font-weight: 400;
        line-height: 17.52px;
        text-align: center;
        text-underline-position: from-font;
        text-decoration-skip-ink: none;
        color: rgb(244, 212, 162);
    }
}

.lbl_c {
    font-family: Antikvarika;
    font-size: 24px;
    font-weight: 400;
    line-height: 24px;
    text-align: center;
    text-underline-position: from-font;
    text-decoration-skip-ink: none;
    color: rgb(255, 226, 189);
    max-width: 56%;
    margin: 0 auto 10px auto;
}

.lbl_t {
    font-family: IBM_Plex_Sans;
    font-size: 17.52px;
    font-weight: 400;
    line-height: 17.52px;
    text-align: center;
    text-underline-position: from-font;
    text-decoration-skip-ink: none;
    color: #fff;
    max-width: 69%;
    margin: 0 auto 12px auto;
}

.offer-block_block-wrap.nxt_ {
    padding: 115px 0;
    background-image: url('assets/images/mountains.avif'), linear-gradient(360deg, #09507e 100%, #09507e 100%);
    background-size: cover;
    background-position: center;

    .m_btn.mid {
        margin-top: 0;
    }
}

.def-block_block-wrap.nxt_ {
    padding: 100px 0 10px 0;
    background-image: url('assets/images/meadow.avif'), linear-gradient(360deg, #09507e 100%, #09507e 100%);
    background-size: cover;
    min-height: unset;
    background-position: center;
}

.shuffleControl:before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    display: block;
    width: 100%;
    height: 100%;
    background-repeat: no-repeat;
    background-position: 50%;
    transition: opacity .2s;
    background-image: url(assets/images/icons/noshuffling.svg);
}

.shuffleControl.m-shuffling:before {
    opacity: 0;
}

.shuffleControl.m-shuffling:after {
    opacity: 1;
}

.shuffleControl {
    position: relative;
    border: 0;
    padding: 0;
    margin: 0;
    height: 100%;
    width: 100%;
    outline: none;
    background: transparent;
}

.skipControl {
    background-repeat: no-repeat;
    padding: 0;
}

.playControls__control.playControls__repeat {
    margin-left: 0;
}

.playControls__control,
.playControls__control:not(:first-child) {
    margin-left: 32.5px;

    &.playControl {
        margin-left: 25.5px;
        margin-right: -7px;
    }
}

.mb_ver {
    display: none;
}

.sp_width {
    min-width: 70%;
    max-width: 70%;
}

.playControls__timeline {
    width: 100%;
}

.playbackTimeline.has-sound {
    visibility: visible;
}

.playbackTimeline__duration,
.playbackTimeline__timePassed {
    font-family: IBM Plex Sans;
    font-weight: 400;
    font-size: 17px;
    line-height: 1.4;
    color: var(--pl_line);
}

.secnd_block-wrap {
    padding: 100px 0;
    background-image: url(assets/images/second_background.avif);
    background-size: cover;
    background-position: center;

    .row_btns {
        display: none;
    }
}

.bcm_img {
    object-fit: cover;
    border: 3px solid #CBAB89;
}

.frame_w {
    position: relative;
    left: 14px;
    z-index: 1;

    &.mirr {
        right: 14px;
        left: unset;
        transform: rotateY(180deg);
    }
}

.def-block_block-wrap {
    padding: 100px 0 10px 0;
    background-image: url('assets/images/clouds.avif'), linear-gradient(360deg, #09507e 100%, #09507e 100%);
    background-size: cover;
    min-height: unset;
    background-position: center;
}

.grad_up {
    position: absolute;
    left: 0;
    bottom: 0;
    width: 100%;
    height: 461px;
    background: linear-gradient(to top, rgba(11, 87, 138, .8), rgba(11, 87, 138, 0));
    z-index: 0;
}

.grad_down {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 611px;
    background: linear-gradient(to bottom, rgba(11, 87, 138, 1), rgba(11, 87, 138, 0));
    z-index: 0;
}

.grad_ {
    position: absolute;
    left: 0;
    width: 250px;
    height: 100%;
    background: linear-gradient(to right, rgba(11, 87, 138, 1), rgba(11, 87, 138, 0));
    z-index: 0;

    &.sd {
        background: linear-gradient(to left, rgba(11, 87, 138, 1), rgba(11, 87, 138, 0));
        right: 0;
        left: unset;
    }
}

.row_btns {
    max-width: 1240px;
    width: 100%;
    justify-content: space-between;
    margin: 3% auto 0 auto;
    padding: 0 20px;

    &.bottom_t {
        justify-content: space-evenly;
        max-width: 1080px;
    }
}

.quick-links .link-icons .link-item.decoration {
    cursor: unset;
}

.round {
    width: 150px;
    height: 150px;
    background: url(assets/images/round.webp);
    background-size: cover;
}

.bot_b-wrap {
    height: 380px;
    width: 100%;
    background-color: #09507e;
}

.m_btn {
    display: flex;
    align-items: center;
    justify-content: center;

    &.tp_wide {
        background: url(assets/images/btn_lg.avif);
        width: 300px;
        height: 92px;
        background-size: cover;
        margin: auto;
    }

    &.wide {
        width: 1400px;
        margin-bottom: 70px;
        height: 92px;
        background: url(assets/images/btn_glow.webp);
        background-size: cover;
        background-repeat: no-repeat;
        background-position: center;
        margin-left: auto;
        margin-right: auto;

        span {
            font-family: Antikvarika;
            font-size: 28px;
            font-weight: 400;
            line-height: 24px;
            text-align: center;
            text-underline-position: from-font;
            text-decoration-skip-ink: none;
        }
    }

    &.mid {
        width: 100%;
        margin-bottom: 90px;
        margin-top: 100px;
        background: url(assets/images/temp.webp);
        background-size: contain;
        background-repeat: no-repeat;
        background-position: center;
        margin-left: auto;
        margin-right: auto;
        width: 663px;
        height: 88px;

        span {
            font-family: Antikvarika;
            font-size: 28px;
            font-weight: 400;
            line-height: 28px;
            text-align: center;
            text-underline-position: from-font;
            text-decoration-skip-ink: none;
            color: #FFE2BD;
        }
    }

    &.light_ {
        width: 210px;
        height: 104px;
        background: url(assets/images/btn_sm.webp);
        margin-bottom: 25px;
        background-size: cover;

        &.t_narrow {
            width: 180px;
            height: 89px;
        }

        &.t_wide {
            width: 260px;
            height: 128px;
        }

        span {
            margin-top: 10px;
        }
    }

    span {
        position: absolute;
        font-family: Antikvarika;
        font-size: 20px;
        font-weight: 400;
        line-height: .9;
        text-align: center;
        text-underline-position: from-font;
        text-decoration-skip-ink: none;
        color: #fff;
    }

    h1 {
        position: absolute;
        font-family: Antikvarika;
        font-size: 20px;
        font-weight: 400;
        line-height: .9;
        text-align: center;
        text-underline-position: from-font;
        text-decoration-skip-ink: none;
        color: #fff;
    }
}

.playbackTimeline__timePassed {
    align-items: center;
    justify-content: right;
    display: flex;
}

.l_item_nm {
    font-family: Prata;
    font-weight: 400;
    font-size: 18px;
    color: var(--font-color1);
    margin-right: auto;
}

.tm_rt {
    font-family: Prata;
    font-weight: 400;
    font-size: 14px;
    line-height: 20px;
    color: var(--font-color1);
    width: 70px;
    display: flex;
    justify-content: center;
}

.show_dots {
    display: none;
}

.app-additional-mobile-menu {
    display: none;
    position: fixed;
    bottom: 0;
    width: 100%;
    z-index: 51;
}

app-header {
    position: fixed;
    width: 100%;
    z-index: 1008;
}

.header_stunt {
    height: 80px;
}

.play_list {
    position: relative;
    list-style-type: decimal;
    cursor: pointer;
    height: 48px;
    padding-right: 27px;

    &:hover {
        background-color: var(--selection);
        border-radius: 3px;
        transition: all .3s;

        .show_time {
            display: none;
        }

        .show_dots {
            display: flex;
        }

        .list__ {
            opacity: 1;
        }
    }
}

.spect_ {
    margin-right: 25px;
}

.form-control {
    width: 100%;
}

.form-control input {
    border: 1px solid;
    border-radius: 7px;
    margin-bottom: 12px;
    padding: 5px 15px;
    width: 100%;
}

.image.image-style-align-center {
    margin-left: auto;
    margin-right: auto;
}

ol,
menu {
    list-style: auto;
}

ul {
    list-style: 13px;
}

.image:not(.image-style-align-right, .image-style-align-left, .image-style-side) {
    margin: 35px auto 35px auto;
    width: fit-content;
}

.image {
    border-radius: 30px;
    overflow: hidden;
    max-width: 100%;
}

.image-style-side {
    float: right;
}

app-login,
app-forgot,
app-registration {
    width: 100%;
}

.reg_form {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin: 90px auto;
    padding: 20px;
    border: 1px solid gray;
    border-radius: 10px;
    background-image: linear-gradient(360deg, #f2ead4 4.3%, #d6c28d);
    width: fit-content;
}

.playbackTimeline__progressBar {
    height: 3px;
    background-color: var(--line_back);
    border-bottom-left-radius: 10px;
    border-top-left-radius: 10px;
}

.playbackTimeline__progressBackground {
    height: 3px;
    background-color: var(--pl_line);
    width: 100%;
    border-radius: 10px;
}

.playbackTimeline__progressBackground,
.playbackTimeline__progressBar,
.playbackTimeline__progressHandle {
    position: absolute;
}

iframe {
    border-radius: 20px;
}

.lot_div {
    background: url(assets/images/icons/lotus_divider.svg);
    background-repeat: no-repeat;
    background-position: center;
    max-width: 100%;
    height: 29px;
    margin: 65px 0 40px 0;
    background-size: contain;
}

.buttons {
    display: flex;
    flex-wrap: wrap;
    margin-right: -30px;
    margin-top: 66px;
}

.buttons a div.button_cont-wrap {
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--button_);
    padding: 10px;
    border-radius: 5px;
    margin-right: 30px;
    text-align: center;
    width: 290px;
    height: 50px;
    transition: all .2s;
    background-size: contain;
    margin-bottom: 15px;
    background-repeat: no-repeat;
    font-family: Prata;
    font-weight: 400;
    font-size: 20px;
    line-height: 20px;
    color: var(--font-color1);
}

.buttons a div.button_cont-wrap:hover {
    background: var(--button_figure);
    transition: all .2s;
    background-size: contain;
    background-repeat: no-repeat;
    color: rgba(255, 255, 255, 1);
}

.html_wrap {
    p img {
        float: left;
    }

    ul {
        list-style: unset;
        margin: 20px 0 20px 40px;
    }

    ol {
        margin: 20px 0 20px 40px;
    }

    .library-context {
        font-family: Prata;
        font-weight: 400;
        font-size: 18px;
        line-height: 22px;
        color: var(--font-color1);
    }

    blockquote {
        margin: 50px 0;
        padding: 85px 20px 70px 20px;
        position: relative;
        text-align: center;
        font-style: italic;
        overflow: hidden;
    }

    blockquote::before {
        content: '';
        background-image: var(--blockquote-before);
        width: 100%;
        height: 40px;
        background-repeat: no-repeat;
        background-position: center;
        position: absolute;
        top: 10px;
        display: flex;
    }

    blockquote::after {
        content: '';
        background-image: var(--blockquote-after);
        width: 100%;
        height: 5px;
        background-repeat: no-repeat;
        background-position: center;
        position: absolute;
        bottom: 20px;
        display: flex;
    }

    blockquote p {
        font-family: Prata;
        font-weight: 500;
        font-style: italic;
        font-size: 28px;
        line-height: 36px;
        color: var(--blockquote-par);
    }

    h1,
    h2,
    h3,
    h4,
    h5,
    h6 {
        font-family: Prata;
        font-weight: 400;
        font-size: 40px;
        line-height: 46px;
        margin: 20px 0;
    }

    // h2 {
    //     font-size: 1.7em;
    //     font-weight: bold;
    //     line-height: 1.2;
    // }

    // h3 {
    //     font-size: 1.4em;
    //     font-weight: bold;
    //     line-height: 1.2;
    // }

    // h4 {
    //     font-size: 13px;
    //     line-height: 1.2;
    // }

    // h5 {
    //     font-size: 13px;
    //     line-height: 1.2;
    // }

    // h6 {
    //     font-size: 13px;
    //     line-height: 1.2;
    // }

    font-family: Prata;
    font-weight: 400;
    font-size: 22px;
    line-height: 32px;
    text-align: justify;
    color: var(--font-color1);
    margin-top: 40px;
    // word-break: break-all;

    figcaption {
        font-family: IBM_Plex_Sans;
        font-size: 22px;
        font-style: italic;
        font-weight: 400;
        line-height: 28px;
        text-underline-position: from-font;
        color: #fff;
        text-align: center;
        margin-top: 20px;
    }

    a.mention {
        font-family: Prata;
        font-weight: 400;
        font-size: 22px;
        line-height: 32px;
        text-align: justify;
        color: rgba(42, 124, 187, 1);
    }

    a.mention:visited {
        color: rgba(105, 77, 164, 1);
    }

    // a.mention:hover {
    //     color: rgba(105, 77, 164, 1);
    // }
}

.library-context {
    position: absolute;
    z-index: 9;
    padding: 12px;
    width: 325px;
    height: 93px;
    border-radius: 20px;
    background: rgba(255, 255, 255, 1);
    box-shadow: 0px 4px 25px 0px var(--shadow);
    cursor: pointer;

    div>p {
        overflow: hidden;
        display: -webkit-box;
        -webkit-line-clamp: 12;
        line-clamp: 12;
        -webkit-box-orient: vertical;
    }

    &.contents_ {
        top: 37px;
        right: 20px;
        height: auto;
        text-align: left;
        width: 468px;
        padding: 28px 0 20px 0;
    }

    &.link_context {
        height: auto;
        width: 391px;
        padding: 30px;
    }

    .redirect-button {
        display: block;
        line-height: 20px;
        text-decoration: underline;
        margin: 25px 0 0 auto;
    }
}

.content-context {
    position: absolute;
    z-index: 9;
    padding: 12px;
    width: 325px;
    height: 93px;
    border-radius: 20px;
    background: rgba(255, 255, 255, 1);
    box-shadow: 0px 4px 25px 0px var(--shadow);
    cursor: pointer;

    div>p {
        overflow: hidden;
        display: -webkit-box;
        -webkit-line-clamp: 12;
        line-clamp: 12;
        -webkit-box-orient: vertical;
    }

    &.contents_ {
        top: 37px;
        right: 20px;
        height: auto;
        text-align: left;
        width: 468px;
        padding: 28px 0 20px 0;
    }

    .redirect-button {
        display: block;
        line-height: 20px;
        text-decoration: underline;
        margin: 25px 0 0 auto;
    }
}

.library-content-context {
    position: absolute;
    z-index: 9;
    padding: 12px;
    width: 325px;
    height: 93px;
    border-radius: 20px;
    background: rgba(255, 255, 255, 1);
    box-shadow: 0px 4px 25px 0px var(--shadow);
    cursor: pointer;

    div>p {
        overflow: hidden;
        display: -webkit-box;
        -webkit-line-clamp: 12;
        line-clamp: 12;
        -webkit-box-orient: vertical;
    }

    &.contents_ {
        top: 37px;
        right: 20px;
        height: auto;
        text-align: left;
        width: 468px;
        padding: 28px 0 20px 0;
    }

    .redirect-button {
        display: block;
        line-height: 20px;
        text-decoration: underline;
        margin: 25px 0 0 auto;
    }
}

.library-context.contents_::before {
    content: "";
    position: absolute;
    background-image: url(assets/images/icons/triangle.svg);
    top: 0px;
    left: 78%;
    transform: translate(-50%, -50%);
    width: 48px;
    height: 18px;
}

.content-context.contents_::before {
    content: "";
    position: absolute;
    background-image: url(assets/images/icons/triangle.svg);
    top: 0px;
    left: 78%;
    transform: translate(-50%, -50%);
    width: 48px;
    height: 18px;
}

.library-content-context.contents_::before {
    content: "";
    position: absolute;
    background-image: url(assets/images/icons/triangle.svg);
    top: 0px;
    left: 78%;
    transform: translate(-50%, -50%);
    width: 48px;
    height: 18px;
}

.library-context::before {
    content: "";
    position: absolute;
    background-image: url(assets/images/icons/triangle.svg);
    top: 0px;
    left: 50%;
    right: 50%;
    transform: translate(-50%, -50%);
    width: 48px;
    height: 18px;
}

.library-context a,
.library-context.link_strt a {
    display: block;
    font-family: Prata;
    font-weight: 400;
    font-size: 18px;
    line-height: 18px;
    color: var(--font-color1);
    width: 50%;
    padding: 5px 0 5px 10px;
}

.image-style-align-left {
    float: left;
    margin-right: 20px;
}

.image-style-align-right {
    float: right;
    margin-left: 20px;
}

.ic_rud {
    width: 107px;
    height: 107px;
    background: url(assets/images/round.webp);
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
    margin-top: 15px;
    margin-bottom: -35px;

    &.wide__ {
        width: 115px;
        height: 115px;
    }
}

.itm_a_wrap {
    margin-top: 60px;
}

.frame_s {
    max-width: unset;
    width: 210px;
    height: 73px;
    z-index: 1;

    &.frame_top {
        background: url(assets/images/frame_top.webp);
        background-size: contain;
        background-repeat: no-repeat;
        margin-top: -35px;
    }

    &.frame_bottom {
        margin-bottom: -12px;
        background: url(assets/images/frame_bottom.webp);
        background-size: contain;
        background-repeat: no-repeat;
    }
}

.quick-links .link-icons .link-item.visible_item {
    display: none;
}

.bottom_ln .frame_bottom {
    display: none;
}

.quick-links .link-icons.complex {
    justify-content: space-between;
    align-items: unset;
    margin-bottom: -315px;
    margin-top: 118px;

    &.bottom_ln {
        max-width: 825px;
        justify-content: space-evenly;
        margin-bottom: 100px;

        .ic_rud {
            position: absolute;
            top: -60px;
            z-index: 1;
            margin: 0;
        }

        .link-item.col {
            min-height: 283px;
            border-radius: 10px;
            justify-content: unset;

            span.txt_ {
                margin-top: 35px;
            }
        }
    }

    .link-item {
        width: 178px;
        max-width: unset;
        justify-content: space-between;
        background-image: url('assets/images/back_card.webp'), linear-gradient(360deg, #F2EAD4 4.3%, #D6C28D 100%);
        background-size: contain;
        background-position: center;

        span {
            margin-top: unset;
            max-width: 90%;
        }

        span.txt_ {
            color: rgba(9, 80, 126, 1);
            margin-bottom: 8px;
            margin-top: -12px;
        }

        span.t_cont {
            font-family: IBM_Plex_Sans;
            font-size: 12px;
            font-weight: 400;
            line-height: 12px;
            text-align: center;
            text-underline-position: from-font;
            text-decoration-skip-ink: none;
            color: rgba(4, 31, 61, 1);
            max-width: 70%;
        }
    }
}

body {
    background: var(--main-background-image);
    background-size: 176px, contain;
    background-repeat: repeat, repeat;

    &:has(.fullscreen-book-dialog-active) {
        overflow: hidden !important;
    }

    &:has(.fullscreen-photo-view) {
        overflow: hidden !important;
    }

    &::-webkit-scrollbar {
        width: 10px;
    }

    &::-webkit-scrollbar-track {
        background: var(--light-color);
    }

    &::-webkit-scrollbar-thumb {
        background: var(--book_about);
        border-radius: 5px;
    }

    &::-webkit-scrollbar-thumb:hover {
        background: var(--text-color);
    }

    &::-webkit-scrollbar:horizontal {
        height: 6px;
    }

    &::-webkit-scrollbar-thumb:horizontal {
        border-radius: 5px;
    }
}

.mw-heading {
    strong {
        font-weight: normal;
    }
}

body::before {
    content: "";
    position: absolute;
    top: 48px;
    left: 0;
    width: 100%;
    height: 425px;
    background: var(--top-cover);
    background-size: cover;
    background-repeat: no-repeat;
    z-index: -1;
}

.bord_w {
    position: absolute;
    width: 1px;
    height: 100%;
    left: 5px;
    z-index: 0;
    background: linear-gradient(90deg, #CBAB89 0%, #7F6F51 48.4%, #CBAB89 100%);

    &.bord_r {
        left: unset;
        right: 5px;
    }
}

.offer-block_block-wrap {
    padding: 115px 0;
    background-image: url('assets/images/universe.avif'), linear-gradient(360deg, #09507e 100%, #09507e 100%);
    background-size: cover;
    background-position: center;
}

li.current {
    background-color: var(--selection);
}

.playbackTimeline__progressHandle {
    border: 1px solid #f50;
    background-color: #f50;
    margin-top: -4px;
}

.playbackTimeline__duration {
    align-items: center;
    justify-content: left;
    display: flex;
    cursor: pointer;
}

.playbackTimeline__progressHandle {
    border: none;
    border-radius: 2px;
    height: 11px;
    width: 4px;
    background-color: var(--line_handle);
    box-sizing: border-box;
    margin-top: -4px;
    margin-left: -4px;
    opacity: 0;
    transition: opacity 0.15s;
}

.playbackTimeline__progressHandle::before {
    content: "";
    display: block;
    position: absolute;
    width: 2px;
    height: 3px;
    background-color: var(--before_handle);
    top: 4px;
    left: -2px;
}

.playbackTimeline__progressHandle::after {
    content: "";
    display: block;
    position: absolute;
    width: 2px;
    height: 3px;
    background-color: var(--before_handle);
    top: 4px;
    right: -2px;
}

.nav_playr {
    margin-left: 30px;
}

.cust_wd {
    width: 22.2%;
}

.pl_title {
    font-family: Prata;
    font-weight: 400;
    font-size: 20px;
    color: #fff;

    &.auth_sm {
        font-size: 17px;
        line-height: 20px;
    }
}

.dt_w {
    font-family: Prata;
    font-weight: 400;
    font-size: 13px;
    line-height: 13px;
    color: var(--text-color);
}

.cal_wrp {
    display: none;

    svg {
        width: 18px;
        height: 19px;
    }
}

.playbackTimeline__progressWrapper:hover {
    .playbackTimeline__progressHandle {
        opacity: 1;
    }
}

.playbackTimeline__progressWrapper {
    position: relative;
    flex-grow: 1;
}

.playbackTimeline__progressWrapper {
    padding: 10px 0;
    margin: 0 8px 0;
    cursor: pointer;
}

.playbackTimeline__timePassed {
    text-align: right;
}

// .playbackTimeline__duration,
// .playbackTimeline__progressWrapper,
// .playbackTimeline__timePassed {
//     line-height: 46px;
// }

.playbackTimeline {
    display: flex;
    font-size: 12px;
}

.ul-list {
    position: relative;
    margin-top: 77px;
    border-top: 1px solid var(--text-color);
}

.hvr {
    padding: 10px 0;
}

.hvr:hover {
    .on_hover {
        display: flex;
        position: fixed;
        flex-direction: column;
        height: fit-content;
        min-height: 106px;
        margin-top: 26px;
        border: 1px solid var(--st_back);
        border-radius: 15px;
        background-color: #fff !important;
        z-index: 10000;
        width: 214px;
    }
}

.spinn_ {
    display: none;
}

.html_wrap a {
    font-family: Prata;
    font-weight: 400;
    font-size: 22px;
    line-height: 32px;
    text-align: justify;
    color: rgb(42, 124, 187);

    &:visited {
        color: rgb(105, 77, 164);
    }

    &:hover {
        color: rgb(105, 77, 164);
    }
}

.html_wrap a:visited {
    span {
        color: rgb(105, 77, 164);
    }

}

.on_hover {
    display: none;
}

.nav_contner {
    height: 58px;
    align-items: center;
    margin-bottom: 5px;
    transform: scale(1);
}

.playControls__control {
    width: 19.5px;
    height: 19.5px;

    &.playControl {
        width: 58px;
        height: 58px;
    }

    &.playControls__repeat {
        width: 24.5px;
        height: 22.5px;
    }

    &.playControls__shuffle {
        width: 26px;
        height: 20.5px;
    }
}

app-goback {
    position: relative;
    z-index: 108;
    color: white;
}

.card-wrap {
    border: 1px solid gray;
    border-radius: 5px;
    margin-top: 10px;
    background-image: linear-gradient(360deg, #f2ead4 4.3%, #d6c28d);
    cursor: pointer;
}

.btn-light {
    display: block;
    border: 1px solid;
    border-radius: 3px;
    background-color: #d0e4f5;
    padding: 3px;
    margin: 20px 20px 0 0;

    &.rounded-tl-full {
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 11px;
        width: 33px;
        height: 38px;
        border: none;
        margin: 20px 12px 0 0;
    }
}

button.search-button {
    color: #e5e7eb;
    border: 1px solid #10b617e8;
    background: rgb(47 37 144 / 50%);
    margin: 20px 15px 0 15px;
    border-radius: 999px;
    transition: .5s;

    &.cs {
        margin-top: 0;
    }

    &:hover {
        background: #0d0d0d;
        color: #f7f7f7;
        transition: .5s;
    }
}

button:disabled {
    opacity: 0.5;
}

.venzs_ {
    display: flex;
    justify-content: space-between;
    overflow: hidden;
}

.img_row.rotate::before {
    content: "";
    background-image: var(--ar);
    background-repeat: no-repeat;
    background-position: center;
    display: flex;
    width: 10px;
    height: 6px;
    margin-right: 6px;
    transform: rotate(180deg);
}

.img_row::before {
    content: "";
    background-image: var(--ar);
    background-repeat: no-repeat;
    background-position: center;
    display: flex;
    width: 10px;
    height: 6px;
    margin-right: 6px;
}

.theme_choose {
    position: relative;
    cursor: pointer;
}

.them_cn {
    cursor: pointer;
    width: 38px;
    height: 38px;
    background-repeat: no-repeat !important;
    background-position: center !important;

    &.Light {
        background: var(--circl_lang), var(--circl_lt);
    }

    &.lightBlue {
        background: var(--circl_lang), var(--circl_md);
    }

    &.Dark {
        background: var(--circl_lang), var(--circl_dk);
    }
}

.theme_choose:hover {
    .lan_switch {
        opacity: 1;
        z-index: 1;
        transition: 0s;
    }
}

.lan_wrap {
    position: relative;

    .lan_switch {
        right: 8px;
    }
}

.lan_wrap:hover {
    .lan_switch {
        opacity: 1;
        z-index: 1;
        transition: 0s;
    }
}

.lan_btn {
    display: flex;
    align-items: center;
    justify-content: center;
    text-transform: uppercase;
    cursor: pointer;
    background: var(--lan_btn);
    background-repeat: no-repeat !important;
    background-position: center !important;
    background-size: contain !important;
    width: 84px;
    height: 38px;
    font-weight: bold;

    p {
        color: var(--menu_font);
        font-weight: 500;
    }
}

.lan_switch {
    opacity: 0;
    z-index: -1;
    transition: .5s;
    right: -15px;
    position: absolute;
    top: 23px;
    border: 2px solid transparent;
    background: linear-gradient(to right, var(--d_back), var(--d_back)),
        linear-gradient(to right, #ffe3a4, #fffaf2);
    background-clip: padding-box,
        border-box;
    background-origin: padding-box,
        border-box;
    border-radius: 15px;
    width: 149px;
    padding: 8px 0 4px 0;
    margin-top: 30px;

    button {
        font-family: Prata;
        font-weight: 400;
        font-size: 18px;
        line-height: 42px;
        letter-spacing: -.3px;
        width: 100%;
        height: 40px;
        background-repeat: no-repeat !important;
        background-position-y: center !important;
        background-position-x: 12%;
        text-align: left;
        padding-left: 50px;
        color: var(--menu_font);
        transition: .2s;

        &.selected_b {
            background-color: rgba(255, 227, 164, 1);
            color: rgba(137, 87, 10, 1);
        }

        &.Light {
            background-image: url(assets/images/icons/light_blue.svg);

            &:hover {
                background-color: rgba(255, 227, 164, 1);
                color: rgba(137, 87, 10, 1);
                transition: .2s;
            }
        }

        &.lightBlue {
            background-image: url(assets/images/icons/bw_blue.svg);

            &:hover {
                background-color: rgba(255, 227, 164, 1);
                color: rgba(137, 87, 10, 1);
                transition: .2s;
            }
        }

        &.def-selection {

            &:hover {
                background-color: rgba(255, 227, 164, 1);
                color: rgba(137, 87, 10, 1);
                transition: .2s;
            }
        }

        &.Dark {
            background-image: url(assets/images/icons/dark_blue.svg);

            &:hover {
                background-color: rgba(255, 227, 164, 1);
                color: rgba(137, 87, 10, 1);
                transition: .2s;
            }

            margin-bottom: 4px;
        }
    }
}

.lan_switch::before {
    content: '';
    background-image: var(--tri_);
    background-repeat: no-repeat;
    background-position: center;
    display: flex;
    width: 49px;
    height: 17px;
    position: absolute;
    right: 7px;
    top: -12px;
}

.rh_block {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 206px;
}

.close_butn {
    cursor: pointer;
    background: var(--close_butn);
    width: 24px;
    height: 24px;
    background-size: cover;
    background-repeat: no-repeat;
}

.close_btn {
    top: 27px;
    cursor: pointer;
    right: 158px;
}

.img_sb {
    position: absolute;
    background: url(assets/images/gloom.avif);
    top: 80px;
    width: 1042px;
    height: 566px;
    z-index: 1;
}

.img_fb {
    position: absolute;
    z-index: 2;
    top: 80px;
    background: url(assets/images/castle.avif);
    width: 1042px;
    height: 566px;
}

.img_mnb {
    position: absolute;
    z-index: 3;
    background: url(assets/images/monk1.avif);
    background-size: cover;
    width: 541px;
    height: 598px;
    left: 0;
    bottom: 0;
    max-height: 90vh;
}

.buttons_wrap {
    position: relative;
    bottom: 3%;
    z-index: 4;
}

.top_block {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    align-items: center;
    position: relative;
    height: 1000px;
    background-image: url(assets/images/mount_.avif);
    background-size: cover;
    background-position: center;
    margin-top: -95px;
    contain: layout paint;
    backface-visibility: hidden;

    @media (max-width: 768px) {
        transform: translateZ(0);
        -webkit-transform: translateZ(0);
        background-attachment: scroll;
        contain: strict;
    }
}

@media (max-width: 1008px) {
    .mar_md {
        margin-top: -70px;
    }

    .html_wrap::first-letter {
        font-size: 70px;
        line-height: 26px;
    }

    .html_wrap h1,
    .html_wrap h2,
    .html_wrap h3,
    .html_wrap h4,
    .html_wrap h5,
    .html_wrap h6 {
        font-size: 28px;
        line-height: 34px;
        margin: 17px 0;
    }
}

@media (max-width: 768px) {
    .carn_b_:not(.rotate) {
        margin-top: -12px;
    }

    .icons_w {

        &.in-favourites {
            .star_w.favr_ {
                width: 14px;
                height: 14px;

                svg.emty_f {
                    width: 14px !important;
                    height: 14px !important;
                }
            }
        }
    }

    .itm_a_wrap {
        margin-top: 25px;
    }

    label.pr_20 {
        font-size: 18px;
        line-height: 20px;
    }

    .style_dialog input.item_ {
        margin-top: 2px;
    }

    .mar_md {
        margin-top: -35px;
    }

    .image {
        border-radius: 15px;
    }

    .scroll_top {
        right: 7% !important;
        background-size: cover !important;
    }

    .top_block {
        &::before {
            background-image: url(assets/images/mount_.avif);
        }
    }

    .html_wrap blockquote::before {
        background-size: contain !important;
    }

    .wrapper_line>div:not(.dec_head) {
        max-width: 100% !important;
    }

    .similar-content-section {
        position: relative;
    }

    .arrows_cont {
        display: flex;
    }

    .html_wrap {
        blockquote::after {
            content: '';
            background-image: var(--blockquote-after_md);
            width: 100%;
            height: 1px;
            background-repeat: no-repeat;
            background-position: center;
            position: absolute;
            bottom: 20px;
            display: flex;
            background-size: cover;
        }
    }

    .lot_div {
        max-width: unset !important;
        width: 125%;
        margin: 45px -12.5% 50px -12.5% !important;
    }

    .similar-item-image {
        width: 280px !important;
        height: 193px !important;
        border-radius: 15px !important;
    }

    .similar-item-title {
        font-size: 18px !important;
        line-height: 22px !important;
    }

    .similar-item-content {
        padding: 20px 0 0 0 !important;
        width: 280px !important;
    }

    .similar-content-title {
        font-size: 28px !important;
        line-height: 28px !important;
    }

    .similar-items {
        margin: 30px -30px 20px 0 !important;
        display: flex !important;
        flex-wrap: unset !important;
        overflow: auto !important;
    }

    .social {
        margin: 50px 0 !important;
    }

    .similar-content-section {
        margin: 10px 0 !important;
    }

    .wrapper_line span.text-color_ {
        font-size: 21px !important;
        line-height: 21px !important;
        margin: 35px 0 35px auto !important;
    }

    .content_wrap_ .social_par {
        font-size: 21px !important;
        line-height: 21px !important;
    }

    .buttons {
        margin-top: 30px !important;
    }

    .content_ {
        margin: 0 15px 15px 0 !important;

        &._telegram {
            width: 26px !important;
            height: 22px !important;
        }

        &._instagram {
            width: 22px !important;
            height: 22px !important;
        }

        &._phone {
            width: 25px !important;
            height: 17px !important;
        }

        &._email {
            width: 22px !important;
            height: 21px !important;
        }
    }

    .tag_item {
        margin-right: 15px !important;
        margin-bottom: 15px !important;
    }

    .html_wrap blockquote {
        padding: 50px 0 35px 0 !important;
    }

    .buttons a div.button_cont-wrap {
        display: flex;
        align-items: center;
        justify-content: center;
        background: var(--button_);
        padding: 10px;
        border-radius: 5px;
        margin-right: 30px;
        text-align: center;
        width: 290px;
        height: 50px;
        transition: all 0.2s;
        background-size: contain;
        margin-bottom: 15px;
        background-repeat: no-repeat;
        font-family: Prata;
        font-weight: 400;
        font-size: 18px;
    }
}

@media (max-width: 600px) {
    .mar_md {
        justify-content: unset;
        margin-top: -40px;
    }

    .html_wrap blockquote {
        margin: 30px 0 !important;
    }

    .scroll_top {
        width: 50px !important;
        height: 50px !important;
    }

    .html_wrap h1,
    .html_wrap h2,
    .html_wrap h3,
    .html_wrap h4,
    .html_wrap h5,
    .html_wrap h6 {
        font-size: 24px !important;
        line-height: 30px !important;
        margin: 15px 0;
    }

    .html_wrap::first-letter {
        font-size: 50px;
    }

    .buttons a div.button_cont-wrap {
        width: 255px !important;
        height: 46px !important;
    }
}

@media (max-width: 500px) {
    .similar-items {
        margin: 30px -30px 12px 0 !important;
    }

    .wrapper_line span.text-color_ {
        font-size: 18px !important;
        line-height: 10px !important;
    }

    .content_wrap_ .social_par {
        font-size: 18px !important;
        line-height: 10px !important;
    }

    .pr_20 {
        font-size: 17px;
    }

    .itm_a_wrap {
        margin-top: 15px;
    }

    .style_dialog .button_brd {
        height: 38px;
        border-radius: 10px;
        font-weight: 400;
        font-size: 17px;
    }

    .style_dialog {
        padding: 38px 24px 10px 24px;
    }

    .style_dialog input.item_ {
        margin-top: 4px;
    }

    label.pr_20 {
        font-size: 14px;
    }

    .h_fixed {
        height: 150px;
    }

    .style_dialog {
        height: 405px;
    }

    .dec_head._background {
        margin-top: -100px !important;
    }

    .image {
        border-radius: 10px;
    }

    .lot_div {
        margin: 20px -12.5% !important;
    }

    .wrapper_line span.text-color_ {
        margin: 25px 0 25px auto !important;
    }

    .similar-item-title {
        font-size: 14px !important;
        line-height: 20px !important;
    }

    .similar-item-content {
        padding: 10px 0 0 0 !important;
        width: 150px !important;
    }

    .wrapper_line {
        padding: 50px 0 40px 0 !important
    }

    .similar-item-image {
        width: 150px !important;
        height: 104px !important;
        border-radius: 10px !important;
    }

    .arr_w {
        width: 34px !important;
        height: 34px !important;
        background-size: cover !important;
    }

    .similar-content-title {
        font-size: 24px !important;
        line-height: 24px !important;
    }

    .buttons a div.button_cont-wrap {
        margin-right: unset !important;
    }

    .buttons {
        justify-content: center !important;
    }
}

@media (max-width: 420px) {
    .mar_md {
        margin-top: -212px;
    }
}

.top_block {
    contain: layout paint; // Optimization hint for browser
    transform: translateZ(0); // Force GPU acceleration
}

.side_head {
    display: flex;
    justify-content: space-between;
    padding: 0 20px 0 255px;
    background: var(--side-back-stripes), var(--main-back-gradient);
    height: 80px;
    background-size: 825px;
    background-position: center;
    background-repeat: repeat-x;
}

.side_head::after {
    content: "";
    position: absolute;
    background-image: var(--bf_gradient);
    background-repeat: no-repeat;
    background-position: center;
    height: 425px;
    width: 100%;
    left: 0;
    top: 80px;
    z-index: -1;
}

.sidebar_cl {
    position: fixed;
    left: -925px;
    top: 0;
    height: 100%;
    width: 825px;
    color: #fff;
    transition: 0.3s;
    z-index: 1008;
    background: var(--side_back);
    overflow: hidden;
}

.sidebar_cl.active {
    left: 0;
}

.sidebar_cl a:not(.spec_) {
    display: block;
    padding: 15px 20px;
    color: #fff;
    text-decoration: none;
}

.main-container {
    margin-left: 0;
    transition: 0.3s;
}

.main-container.active {
    margin-left: 250px;
}

.logo {
    max-width: 80px;
}

.carousel-container.cnt {
    .carousel-item {
        min-width: 25%;
    }
}

.carousel-container.trp {
    .carousel-item {
        min-width: 33.33%;
    }

    @media (max-width: 1079px) {
        .carousel-item {
            min-width: 50%;
        }
    }


    @media (max-width: 768px) {
        .carousel-item {
            min-width: 100%;
        }
    }
}

.carousel-date.mb_ {
    margin-bottom: 20px;
}

.clamp_2 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    -webkit-box-orient: vertical;
}

.carousel-container {
    position: relative;
    overflow: hidden;
    width: 100%;
    max-width: 1200px;
    margin: auto;

    &.middle {
        margin: unset;

        &.with_decor {
            .frame_w {
                position: absolute;
                left: 42px;
                width: 161px;

                &.mirr {
                    right: 42px;
                    left: unset;
                }
            }
        }
    }

    .carousel {
        display: flex;
        transition: transform 0.5s ease;
    }

    .carousel-item {
        min-width: 75%;
        box-sizing: border-box;
        text-align: center;
        padding: 20px;

        .carousel-content {
            color: #fff;
            position: absolute;
            z-index: 11;
            bottom: 55px;
            width: 346px;

            .carousel-date {
                display: flex;
                align-items: center;
                justify-content: center;
                font-family: IBM_Plex_Sans;
                font-size: 12px;
                font-weight: 400;
                line-height: 12px;
                color: #F4D4A2;
                text-align: center;
                text-underline-position: from-font;
                text-decoration-skip-ink: none;
            }

            .carousel-title {
                font-family: Antikvarika;
                font-size: 28px;
                font-weight: 400;
                line-height: 28px;
                text-align: center;
                text-underline-position: from-font;
                text-decoration-skip-ink: none;
                max-width: 70%;
                margin: auto;
                color: #fff;

                &.gold {
                    color: rgba(255, 226, 189, 1);
                    margin-bottom: 20px;
                }
            }
        }
    }
}

.profile-playlist {
    max-width: 900px;
    margin: 90px auto;
    padding: 20px;
    border: 1px solid gray;
    border-radius: 10px;
    background-image: linear-gradient(360deg, #f2ead4 4.3%, #d6c28d);
}

.profile-playlist h1 {
    font-weight: 500;
    font-size: 22px;
}

.button_b {
    background-color: #f1f1f1;
    border: none;
    padding: 10px 20px;
    cursor: pointer;
    font-size: 16px;
    outline: none;
    border-radius: 5px;

    &.active {
        background-image: linear-gradient(360deg, #0E5788 4.3%, #062b55);
        color: white;
        background-position: center;
    }

    &.mb {
        margin-bottom: 15px;
    }
}

.tabs .button_b.active {
    background-image: linear-gradient(360deg, #0E5788 4.3%, #062b55);
    ;
    color: white;
    background-position: center;
}

.tabs button:hover {
    background-color: #ddd;
}

.frame_ {
    position: absolute;
    z-index: 11;
    width: 346px;
    height: 346px;
    background: url(assets/images/frame.webp);
    background-size: contain;
}

.carousel-arrow {
    position: absolute;
    top: 50%;
    color: white;
    border: none;
    font-size: 2em;
    cursor: pointer;
    padding: 10px;
    transform: translateY(-50%);
    z-index: 10;
    background: url(assets/images/btn_l.webp);
    background-size: contain;
    width: 84px;
    height: 44px;

    &.left-arrow {
        left: -88px;
    }

    &.right-arrow {
        right: -88px;
        transform: rotate(180deg);
    }
}

.top_neg {
    position: absolute;
    bottom: -17px;
    width: 100%;
    z-index: 8;
}

.side_blbc {
    position: absolute;
    top: -50vh;
    width: 100%;
    height: 200vh;
    z-index: 109;
    background-color: var(--blur_main);
}

.side_ {
    padding: 32px 10px 52px 255px;
}

.social_side_ {
    display: flex;
    padding-left: 255px;
}

.soc_ {
    width: 40px;
    height: 40px;
    background-size: contain !important;
    background-repeat: no-repeat !important;
    cursor: pointer;
    margin-right: 15px;

    &.tel_ {
        background: url(assets/images/icons/telegr.svg);
    }

    &.yt_ {
        background: url(assets/images/icons/yout.svg);
    }

    &.vk_ {
        background: url(assets/images/icons/vk.svg);
    }

    &.fb_ {
        background: url(assets/images/icons/fbk.svg);
    }

    &.in_ {
        background: url(assets/images/icons/instgrm.svg);
    }
}

.sidebar_cl {
    .button_img {
        a {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 203px;
            height: 50px;
            background: var(--btn_main);
            background-size: contain !important;
            background-position: center !important;
            background-repeat: no-repeat !important;
            font-family: Prata;
            font-weight: 400;
            font-size: 17px;
            line-height: 17px;
            color: rgba(211, 146, 60, 1);
        }

        &.reg {
            a {
                background: var(--btn_main_r);
                color: rgba(255, 255, 255, 1);
                margin-left: 40px;
            }
        }
    }
}

.star_ {
    position: relative;
    top: -45px;
    width: 55px;
    height: 55px;
    background-repeat: no-repeat;
    background-position: center;
    background: url(assets/images/star.webp);
    background-size: contain;
}

.tablet_mob {
    display: none;
}

.show_msd {
    display: none;
}

.quick-links {
    position: relative;
    z-index: 1;

    .link-icons {
        display: flex;
        justify-content: space-evenly;
        align-items: baseline;
        max-width: 1170px;
        margin: auto;

        .link-item {
            display: flex;
            flex-direction: row;
            justify-content: center;
            align-items: center;
            text-align: center;
            cursor: pointer;
            max-width: 150px;

            &.col {
                flex-direction: column;
            }

            span {
                font-family: Antikvarika;
                font-size: 24px;
                font-weight: 400;
                line-height: 24px;
                text-align: center;
                text-underline-position: from-font;
                text-decoration-skip-ink: none;
                color: #FFE2BD;
                max-width: 95%;
                margin-top: 22px;
            }

            .image_w {
                display: flex;
                align-items: center;
                justify-content: center;

                img.sign {
                    position: absolute;
                    scale: 0.5;
                }
            }
        }
    }
}

header {
    position: relative;
    z-index: 108;
}

.divide_ {
    width: 100%;
    align-items: center;
}

.img_row {
    display: flex;
    align-items: center;
}

.ms_wrap {
    margin: 12px 0 0 57px;
}

.item_menu {
    display: flex;
    flex-direction: column;
    font-family: Prata;
    font-weight: 400;
    font-size: 22px;
    line-height: 32px;
    margin-bottom: 20px;
    color: var(--font-color1);
    cursor: pointer;

    li {
        font-family: Prata;
        font-weight: 400;
        font-size: 20px;
        line-height: 44px;
        color: var(--font-color1);
        text-align: left;
        text-underline-position: from-font;
        text-decoration-skip-ink: none;
    }

}

.list_menu {
    padding: 39px 10px 10px 255px;
    overflow: auto;
    max-height: 62vh;
}

.burger {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 16px;
    padding: 10px 0;
    position: relative;
    z-index: 1008;
    background: var(--burger);
    width: 35px;
    height: 19px;
    background-position: center;
    background-repeat: no-repeat;
    box-sizing: content-box;
}

select.form-select_ {
    height: 30px;
}

.menu_wrap {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 125px;
    background: var(--main-back-stripes), var(--main-back-gradient);
    height: 80px;
    background-size: 1920px;
    background-position: center;
    background-repeat: repeat-x;
}

.similar-content-title {
    font-family: Prata;
    font-weight: 400;
    font-size: 40px;
    line-height: 32px;
    color: var(--font-color1);
}

.tags_cont {
    display: flex;
    flex-wrap: wrap;
    margin-top: 80px;
}

.tag_item {
    border: 1px solid var(--font-color1);
    border-radius: 15px;
    padding: 12px 22px;
    font-family: Prata;
    font-weight: 400;
    font-size: 24px;
    line-height: 24px;
    color: var(--font-color1);
    cursor: pointer;
    margin-right: 20px;
    margin-bottom: 20px;

    &:hover {
        border: 1px solid var(--blockquote-par);
        background: var(--blockquote-par);
        color: rgba(255, 255, 255, 1);
    }
}

.scroll_top {
    position: fixed;
    width: 60px;
    height: 60px;
    background-size: cover !important;
    background: var(--scroll_top);
    cursor: pointer;
    bottom: 8%;
    right: 6%;
    z-index: -1;
    opacity: 0;
    transition: opacity .5s;

    &.is-visible {
        opacity: 1;
        transition: opacity .5s;
        z-index: 11;
    }
}

.wrapper_line span.text-color_ {
    display: block;
    font-family: Prata;
    font-weight: 400;
    font-size: 24px;
    line-height: 24px;
    text-align: right;
    color: var(--font-color1);
    margin: 50px 0 50px auto;
}

.icons_w {

    .icon-wrap:not(.cal_w,
        .clock_w) {
        cursor: pointer;
    }

    span.text-color {
        font-family: Prata;
        font-weight: 400;
        font-size: 17px;
        line-height: 18px;
        color: var(--font-color1);
    }

    &.in-favourites {
        .star_w {
            background: var(--star_dark);
            height: 24px;
            width: 25px;
        }

        svg.emty_f_hover {
            display: none;
        }

        svg.emty_f {
            display: none;
        }
    }

    svg.emty_f_hover {
        display: none;
    }

    &.is-liked {
        .like_w {
            background: var(--heart_dark);
            height: 22px;
            width: 24px;
        }

        svg.emty_l_hover {
            display: none;
        }

        svg.emty_l {
            display: none;
        }
    }

    svg.emty_l_hover {
        display: none;
    }

    &:hover {
        &.no_hover {
            span.text-color {
                color: var(--font-color1);
            }
        }

        span.text-color {
            color: var(--text-color);
        }

        // .cal_w {
        //     background: var(--calendar);
        //     height: 27px;
        //     width: 24px;

        //     svg {
        //         opacity: 0;
        //     }
        // }

        // .clock_w {
        //     background: var(--clock);
        //     height: 24px;
        //     width: 24px;

        //     svg {
        //         opacity: 0;
        //     }
        // }

        &:not(.in-favourites) {
            .star_w {
                svg.emty_f {
                    display: none;
                }

                svg.emty_f_hover {
                    display: block;
                }
            }
        }

        &:not(.is-liked) {
            .like_w {
                svg.emty_l {
                    display: none;
                }

                svg.emty_l_hover {
                    display: block;
                }
            }
        }

        &.is-liked {
            .like_w {
                background: var(--heart);
                height: 22px;
                width: 24px;
            }
        }

        &.in-favourites {
            .star_w {
                background: var(--star);
                height: 24px;
                width: 25px;
            }
        }

        .share_w {
            background: var(--share);
            height: 24px;
            width: 24px;

            svg {
                opacity: 0;
            }
        }

        .content_w {
            background: var(--content);
            height: 18px;
            width: 22px;

            svg {
                opacity: 0;
            }
        }
    }
}

.icon-wrap {
    background-size: contain !important;
    background-repeat: no-repeat !important;
    margin-top: -3px;
}

.m_grid {
    max-width: 1170px;
    width: 100%;
    display: flex;
    justify-content: space-between;

    &.nav_wrap {
        max-width: 1260px;
    }
}

.f_par {
    font-family: Prata;
    font-weight: 400;
    font-size: 20px;
    line-height: 30px;
    color: var(--menu_font);
    margin-top: 30px;
}

.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 5px 0;
    position: relative;
    margin: 0 auto;
}

.navbar ul {
    display: flex;
    list-style: none;
    padding: 0;
    margin: 0;
}

.ms_wrap ul {
    list-style: initial;
}

.navbar li {
    display: flex;
    align-items: center;
    margin-right: 24px;
}

.navbar li::before {
    content: '';
    background-image: var(--arrow_d);
    background-repeat: no-repeat;
    background-position: center;
    display: flex;
    width: 10px;
    height: 6px;
    margin-right: 6px;
}

.navbar li.item::before {
    background-image: unset;
    width: unset;
    height: unset;
}

.navbar li.button_img_before {
    width: 178px;
    height: 37px;
    justify-content: center;
    margin: 0 25px 0 0;

    button {
        margin-top: 4px;
    }
}

.navbar .button_img_before a {
    font-family: Prata;
    font-weight: 400;
    font-size: 17px;
    line-height: 18px;
    color: var(--menu_font);
}

.navbar li.item.button_img_before::before {
    background-image: var(--btt_top);
    background-repeat: no-repeat;
    background-position: center;
    background-size: contain;
    width: 178px;
    height: 37px;
    position: absolute;
    right: 0;
    margin: 0;
}

.navbar li.item.serch::before {
    background-image: var(--serch);
    background-repeat: no-repeat;
    background-position: center;
    background-size: contain;
    width: 21px;
    height: 21px;
    margin: 0;
}

.navbar a {
    font-family: Prata;
    font-weight: 400;
    font-size: 19px;
    line-height: 20px;
    vertical-align: middle;
    color: var(--menu_font);

    &.rout_l {
        position: absolute;
        width: 21px;
        height: 21px;
    }
}

.user-menu {
    display: flex;
    align-items: center;
}

.user-dropdown {
    color: var(--menu_font);
    padding: 5px 14px;
    cursor: pointer;
    line-height: 1;
    letter-spacing: 0.02em;
    text-align: left;
    text-underline-position: from-font;
    text-decoration-skip-ink: none;
    font-family: Prata;
    font-weight: 400;
    font-size: 17px;
    margin-bottom: -5px;
}

.user-dropdown.has-arrow {
    display: flex;
    align-items: center;
    white-space: nowrap;
}

.user-dropdown.has-arrow::after {
    content: '';
    background-image: var(--arrow_d);
    background-repeat: no-repeat;
    background-position: center;
    display: flex;
    width: 10px;
    height: 6px;
    margin-left: 7px;
    margin-top: -2px;
}

.user_logo_ {
    width: 38px;
    height: 38px;
    background: var(--signin);
    background-size: contain !important;
    background-repeat: no-repeat !important;

    &.authorized_ {
        background: var(--authorized);

        img {
            position: relative;
            z-index: 1;
            width: 100%;
            border-radius: 50%;
            object-fit: cover;
            height: 100%;
        }
    }
}

.notifications {

    span {
        display: flex;
        justify-content: center;
        align-items: center;
        position: absolute;
        top: 0px;
        right: -6px;
        width: 14px;
        height: 14px;
        border-radius: 50%;
        font-family: IBM_Plex_Sans;
        font-size: 10px;
        font-weight: 400 !important;
        color: #fff;
        line-height: 20px;
        text-align: center;
        text-underline-position: from-font;
        text-decoration-skip-ink: none;
        background: rgba(42, 124, 187, 1);
    }
}

app-layout {
    width: 100%;
}

.content-height_wrap {
    display: flex;
    min-height: 100vh;
    background-size: 15%;
}

.notifications span {
    font-weight: bold;
}

b,
strong {
    font-weight: normal;
}

.fancybox__container {
    position: fixed;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    direction: ltr;
    display: flex;
    flex-direction: column;
    box-sizing: border-box;
    margin: 0;
    padding: 0;
    color: #f8f8f8;
    -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
    overflow: visible;
    z-index: 1050;
    outline: none;
    transform-origin: top left;
    text-size-adjust: 100%;
    overscroll-behavior-y: contain;
}

.fancybox__backdrop {
    position: fixed;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    z-index: -1;
    background: #000000a6;
    opacity: 1;
    will-change: opacity;
}

.fancybox__carousel {
    position: relative;
    box-sizing: border-box;
    flex: 1;
    min-height: 0;
    z-index: 10;
    overflow-y: visible;
    overflow-x: clip;
}

.fancybox__viewport {
    width: 100%;
    height: 100%;
}

.fancybox__track {
    display: flex;
    margin: 0 auto;
    height: 100%;
    transform: matrix(1, 0, 0, 1, 0, 0);
}

.fancybox__container:not(.is-compact) .fancybox__slide.has-close-btn {
    padding-top: 40px;
}

.fancybox__slide {
    flex: 0 0 auto;
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100%;
    height: 100%;
    margin: 0 0 0;
    padding: 4px;
    overflow: auto;
    overscroll-behavior: contain;
    transform: translate3d(0, 0, 0);
    backface-visibility: hidden;
}

.fancybox__slide::before,
.fancybox__slide::after {
    content: "";
    flex: 0 0 0;
    margin: auto;
}

.fancybox__container *,
.fancybox__container *::before,
.fancybox__container *::after {
    box-sizing: inherit;
}

.fancybox__content {
    align-self: center;
    display: flex;
    flex-direction: column;
    position: relative;
    margin: 0;
    padding: 2rem;
    max-width: 100%;
    color: #374151;
    background: #fff;
    cursor: default;
    border-radius: 0;
    z-index: 20;

    .btn-light {
        display: block;
        border: 1px solid;
        border-radius: 3px;
        background-color: #d0e4f5;
        padding: 3px;
        margin: 20px 20px 0 0;
    }
}

.ng-select .ng-select-container {
    cursor: default;
    display: flex;
    outline: none;
    overflow: hidden;
    position: relative;
    width: 100%;
}

.ng-select.ng-select-multiple .ng-select-container .ng-value-container {
    flex-wrap: wrap;
}

.ng-select.ng-select-multiple .ng-select-container .ng-value-container .ng-placeholder {
    z-index: 1;
}

.ng-select.ng-select-multiple .ng-select-container .ng-value-container .ng-placeholder {
    position: absolute;
}

.ng-select .ng-select-container .ng-value-container {
    display: flex;
    flex: 1;
}

.ng-select.ng-select-multiple .ng-select-container .ng-value-container .ng-value .ng-value-icon {
    display: none;
}

.ng-select {
    position: relative;
    display: block;
    box-sizing: border-box;
}

.fancybox__content>.f-button.is-close-btn {
    position: absolute;
    top: -38px;
    right: 0;
    opacity: .75;
}

.ng-dropdown-panel .scroll-host {
    border: 1px solid;
    background: cornsilk;
    padding: 5px;
}

.f-button svg {
    width: 30px;
    height: 30px;
    fill: #fff;
    stroke: #fff;
    stroke-width: 2px;
    stroke-linecap: round;
    stroke-linejoin: round;
    transition: opacity .15s ease;
    pointer-events: none;
}

.nams_wrap {
    background: url(assets/images/icons/namaste_.svg);
    background-position: center;
    background-size: contain;
    background-repeat: no-repeat;
    height: 38px;
    max-width: 336px;
    margin: 32px auto 28px auto;
}

.x_bt {
    position: absolute;
    right: 16px;
    top: 14px;
    width: 20px;
    height: 20px;
    background: var(--x_bt);
    background-size: contain;
    background-repeat: no-repeat;
    cursor: pointer;
}

dialog:-internal-dialog-in-top-layer::backdrop {
    background: var(--blur_main);
}

dialog {
    border-radius: 8px;
    padding: 20px;

    &:focus-visible {
        outline: none;
    }

    &.stylized_ {
        width: 426px;
        border-radius: 20px;
        padding: 46px 0 65px 0;

        .b_wrap {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
        }
    }

    &.stylized_wide {
        width: 690px;
        padding: 54px 0 30px 0;
        border-radius: 20px;
        overflow: visible;
        position: fixed;
        top: 0;

        &.h_auto {
            height: fit-content;
            padding: 52px 0 79px 0;

            .cont_mod {
                height: 100%;
                overflow: visible;
            }
        }

        .cont_mod {
            overflow: visible;
            width: 100%;
            margin: auto;
            width: 75%;
        }

        .x_bt {
            right: 21px;
            top: 21px;
        }
    }
}

.checkbox-container {
    display: flex;
    align-items: center;
}

.format-options {
    position: relative;
    display: inline-flex;
    align-items: center;
    cursor: pointer;
    justify-content: space-between;
    margin-top: 40px;
    cursor: pointer;

    input.checkbox_ {
        width: 32px;
        height: 32px;
    }

    label {
        font-family: Prata;
        font-weight: 400;
        font-size: 20px;
        line-height: 25px;
        color: var(--font-color1);
        cursor: pointer;
    }

    input[type="checkbox"] {
        opacity: 0;
        position: absolute;
        z-index: 3;
        margin: 0;
        cursor: pointer;
    }

    span {
        width: 32px;
        height: 32px;
        background-color: #fff;
        border-radius: 50%;
        border: 1px solid var(--text-color);
        margin-right: 11px;
        transition: background-color 0.3s;
        z-index: -1;
        display: inline-block;
        position: relative;
        cursor: pointer;
    }

    input[type="checkbox"]:checked+span {
        background-color: var(--text-color);
    }

    input[type="checkbox"]:checked+span::after {
        content: '';
        position: absolute;
        left: 10px;
        top: 4px;
        width: 10px;
        height: 16px;
        border: solid white;
        border-width: 0 3px 3px 0;
        transform: rotate(45deg);
        cursor: pointer;
    }
}

.else_bt {
    display: flex;
    align-items: center;
    justify-content: center;
    background: url(assets/images/icons/bt_bord_.svg);
    width: 187px;
    height: 50px;
    background-size: contain !important;
    margin-bottom: 15px;
    background-repeat: no-repeat !important;
    font-family: Prata;
    font-weight: 400;
    font-size: 20px;
    line-height: 20px;
    color: var(--font-color1);
    text-align: center;
}

.auth_head {
    font-family: Prata;
    font-weight: 400;
    font-size: 14px;
    line-height: 14px;
    color: var(--font-color1);
    text-align: left;
    margin-bottom: 6px;
}

.auth_p {
    font-family: Prata;
    font-weight: 400;
    font-size: 20px;
    line-height: 24px;
    color: var(--font-color1);
    text-align: center;
}

button.else_bt.reg_bt {
    background: url(assets/images/icons/Button1_1_.svg);
    width: 258px;
    height: 55px;
}

.ok-button {
    padding: 5px 20px;
    border-radius: 10px;
    color: white;
    background-color: rgb(173, 59, 59);
}

.cancel-button {
    padding: 5px 20px;
    border-radius: 10px;
    color: white;
    background-color: #26691d
}




@media (max-width: 1008px) {
    .html_wrap blockquote {
        padding: 70px 0 50px 0 !important;
    }

    .html_wrap blockquote p {
        font-size: 18px !important;
        line-height: 26px !important;
    }

    .html_wrap blockquote::before {
        width: 132%;
        left: -16%;
    }

    .html_wrap blockquote::after {
        width: 130%;
        left: -15%;
    }
}

@use "./assets/styles/responsive.scss";